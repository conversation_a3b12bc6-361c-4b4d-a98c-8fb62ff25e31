import { Building } from "@/types/buildings";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { ArrowRight, MapPin } from "lucide-react";
import { useLocale, useTranslations } from "next-intl";

export default function BuildingCards({ data }: { data: Building[] }) {
  const locale = useLocale();
  const t = useTranslations("cities");
  return (
    <div className="grid grid-cols-[repeat(auto-fit,minmax(280px,1fr))] gap-6">
      {data.map((build) => (
        <div key={build.id} className="group cursor-pointer">
          <div className="relative transform overflow-hidden rounded-2xl shadow-lg transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl">
            <div className="aspect-w-4 aspect-h-5">
              <Image
                alt={locale === "ar" ? build.nameAr : build.nameEn}
                className="h-80 w-full object-cover transition-transform duration-500 group-hover:scale-110"
                src={build.image}
                width={500}
                height={500}
              />
            </div>
            <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-transparent to-transparent opacity-100 transition-opacity duration-300" />
            <div className="absolute right-0 bottom-0 left-0 flex h-full translate-y-4 transform flex-col justify-end p-6 py-10 text-white transition-transform duration-300 group-hover:translate-y-0">
              <h3 className="mb-2 text-xl font-semibold">
                {locale === "ar" ? build.nameAr : build.nameEn}
              </h3>
              <p className="mb-4 text-sm text-white/80">
                {build.unitsCount}+ {t("cities.buildings")}
              </p>
              <div className="flex items-center gap-3 transition-all delay-100 duration-300">
                <Link
                  href={`/buildings?cityId=${build.id}`}
                  className="group/button inline-flex transform items-center gap-2 rounded-full border border-white/30 bg-white/20 px-4 py-2.5 text-sm font-medium text-white shadow-lg backdrop-blur-sm transition-all duration-300 hover:scale-105 hover:border-white/50 hover:bg-white/30 hover:shadow-xl active:scale-95"
                >
                  <MapPin className="h-4 w-4 opacity-80" />
                  <span>{t("cities.viewUnits")}</span>
                  <ArrowRight className="h-4 w-4 transform transition-transform duration-300 group-hover/button:translate-x-1 rtl:rotate-180" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
