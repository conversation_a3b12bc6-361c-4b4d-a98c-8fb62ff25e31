"use client";
import { useGetBuildingsByCity } from "@/hooks/buildings";
import { useLocale, useTranslations } from "next-intl";
import BuildingHeader from "./components/BuildingHeader";
import BuildingMap from "./components/BuildingMap";
import BuildingCards from "./components/BuildingCards";

export default function page() {
  const locale = useLocale();
  const t = useTranslations("cities");

  const { data } = useGetBuildingsByCity({});

  return (
    <div className="flex-1 p-12">
      <div className="mx-auto max-w-7xl">
        <BuildingHeader />
        <div className="grid items-start gap-4">
          {/* <BuildingMap /> */}
          <BuildingCards data={data?.items ?? []} />
        </div>
      </div>
    </div>
  );
}
